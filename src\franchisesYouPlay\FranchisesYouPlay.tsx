import React, { FC, ReactElement } from "react";
import InterestedCreatorsFranchisesYouPlayPage from "./InterestedCreatorsFranchisesYouPlayPage/InterestedCreatorsFranchisesYouPlayPage";
import { BrowserAnalytics, CloseHandler, Dispatch, ErrorHandling, State, ValidationError } from "../utils";
import { NextRouter } from "next/router";
import { InterestedCreator } from "../Browser/InterestedCreatorsService";
import { Configuration } from "../information/Information";

export type LayoutButtons = {
  yes: string;
  no: string;
  cancel: string;
  next: string;
  submit: string;
  close: string;
};

export type FranchiseMessages = {
  primaryFranchise: string;
};

export type FranchiseLabels = {
  primaryFranchise: string;
  loadMore: string;
};

export type FranchisesYouPlayLabels = {
  title: string;
  description: string;
  confirmationDesc1: string;
  confirmationDesc2: string;
  modalConfirmationTitle: string;
  buttons: LayoutButtons;
};
export type ModalLabels = {
  title: string;
  description: string;
  cancel: string;
  close: string;
};

export type FranchisesYouPlayFormLabels = {
  buttons?: LayoutButtons;
  messages: FranchiseMessages;
  labels: FranchiseLabels;
  primaryFranchiseTitle: string;
  primaryFranchiseSubTitle: string;
  secondaryFranchiseTitle: string;
  secondaryFranchiseSubTitle: string;
};

export type FranchisesYouPlayFallbackImages = {
  primaryFranchisefallbackImage: string;
  secondaryFranchisefallbackImage: string;
};

type InterestedCreatorFranchisesYouPlayProps = {
  interestedCreator: InterestedCreator;
  handleCancelRegistration: () => void;
  analytics: BrowserAnalytics;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  labels: Labels;
  state: State;
  stableDispatch: Dispatch;
  errorHandling: ErrorHandling;
  redirectedToNextStepUrl: string;
  configuration: Configuration;
  onClose: () => void;
  showConfirmation: boolean;
  setShowConfirmation: (value: boolean) => void;
  router: NextRouter;
  locale: string;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  franchisesYouPlayFallbackImages: FranchisesYouPlayFallbackImages;
  basePath?: string;
};

export type Labels = {
  franchisesYouPlay: FranchisesYouPlayLabels;
  franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels;
  layout: {
    buttons: LayoutButtons;
    main: {
      unhandledError: string;
    };
  };
};

const InterestedCreatorFranchisesYouPlay: FC<InterestedCreatorFranchisesYouPlayProps> = ({
  interestedCreator,
  analytics,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  labels,
  state,
  stableDispatch,
  handleCancelRegistration,
  configuration,
  errorHandling,
  redirectedToNextStepUrl,
  onClose,
  showConfirmation,
  setShowConfirmation,
  router,
  locale,
  errorToast,
  franchisesYouPlayFallbackImages,
  basePath,
}) => {
  const { isValidationError = false, isError = false } = state;
  const { franchisesYouPlay, layout, franchisesYouPlayFormLabels } = labels;
  const layoutButton = {
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    cancel: layout.buttons.cancel,
    next: layout.buttons.next,
    submit: layout.buttons.submit,
    close: layout.buttons.close
  };

  return (
    <div className="interested-creators-ui">
      <InterestedCreatorsFranchisesYouPlayPage
        interestedCreator={interestedCreator}
        franchisesYouPlayLabels={{ ...franchisesYouPlay, buttons: layoutButton }}
        franchisesYouPlayFormLabels={{ ...franchisesYouPlayFormLabels, buttons: layoutButton }}
        stableDispatch={stableDispatch}
        showConfirmation={showConfirmation}
        setShowConfirmation={setShowConfirmation}
        handleCancelRegistration={handleCancelRegistration}
        onClose={onClose}
        isError={isError as boolean}
        isValidationError={isValidationError as ValidationError[]}
        errorToast={errorToast}
        unhandledError={layout.main.unhandledError}
        router={router}
        locale={locale}
        analytics={analytics}
        INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
        errorHandling={errorHandling}
        configuration={configuration}
        redirectedToNextStepUrl={redirectedToNextStepUrl}
        franchisesYouPlayFallbackImages={franchisesYouPlayFallbackImages}
        basePath={basePath}
      />
    </div>
  );
};

export default InterestedCreatorFranchisesYouPlay;
