import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { NextRouter } from "next/router";
import InterestedCreatorsService, { InterestedCreator } from "../../Browser/InterestedCreatorsService";
import { BrowserAnalytics } from "../../utils";
import { anInterestedCreator } from "../../../test/factories/InterestedCreatorFactories";
import { interestedCreatorsFranchisesYouPlayFormLabels } from "../../../test/translations/InterestedCreatorsFranchisesYouPlayForm";
import InterestedCreatorsFranchisesYouPlayForm from "./InterestedCreatorsFranchisesYouPlayForm";
import { Configuration } from "../../information/Information";

jest.mock("../../Browser/InterestedCreatorsService");

describe("InterestedCreatorsFranchisesYouPlayForm", () => {
  const analytics = {} as unknown as BrowserAnalytics;
  const franchises = [
    { value: "Apex", label: "Apex", image: "" },
    { value: "Fifa", label: "Fifa", image: "" }
  ];
  const interestedCreator = anInterestedCreator({
    nucleusId: 1234567,
    defaultGamerTag: "RiffleShooter",
    originEmail: "<EMAIL>",
    preferredLanguage: { code: "en_US", name: "English" }
  }) as InterestedCreator;
  const router = { locale: "en-us", push: jest.fn() } as unknown as NextRouter;
  const interestedCreatorWithFranchises: InterestedCreator = {
    ...interestedCreator,
    preferredFranchises: [
      { id: "Apex", type: "PRIMARY" },
      { id: "Fifa", type: "SECONDARY" }
    ]
  };
  const configuration = { metadataClient: {}, applicationsClient: {}, programCode: "affiliate" } as Configuration;
  const interestedCreatorsFranchisesYouPlayFormProps = {
    franchises,
    interestedCreator,
    franchisesYouPlayFormLabels: interestedCreatorsFranchisesYouPlayFormLabels,
    stableDispatch: jest.fn(),
    onClose: jest.fn(),
    router,
    analytics,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    errorHandling: jest.fn(),
    configuration: configuration,
    redirectedToNextStepUrl: "/interested-creators/complete",
    franchisesYouPlayFallbackImages: {
      primaryFranchisefallbackImage: "",
      secondaryFranchisefallbackImage: ""
    },
    basePath: "/support-a-creator"
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("enables next button when primary franchise is selected", async () => {
    render(<InterestedCreatorsFranchisesYouPlayForm {...interestedCreatorsFranchisesYouPlayFormProps} />);
    const submitButton = screen.getByText(interestedCreatorsFranchisesYouPlayFormLabels.buttons.submit);
    expect(submitButton).toBeDisabled();
    await userEvent.click(
      screen.getByRole("textbox", { name: interestedCreatorsFranchisesYouPlayFormLabels.labels.primaryFranchise })
    );

    await userEvent.click(screen.getByText("Apex", { selector: ".search-option-label" }));

    await waitFor(() => expect(submitButton).toBeEnabled());
  });

  it("pre-populates the form with current preferred primary franchise", async () => {
    render(
      <InterestedCreatorsFranchisesYouPlayForm
        {...interestedCreatorsFranchisesYouPlayFormProps}
        interestedCreator={interestedCreatorWithFranchises}
      />
    );

    await waitFor(() => expect(screen.getByDisplayValue("Apex")).toHaveClass("search-text-field"));
  });

  it("pre-populates the form with current preferred secondary franchise", async () => {
    render(
      <InterestedCreatorsFranchisesYouPlayForm
        {...interestedCreatorsFranchisesYouPlayFormProps}
        interestedCreator={interestedCreatorWithFranchises}
      />
    );

    expect(screen.getByText("Select more Franchises")).toBeInTheDocument();
    expect(screen.getAllByRole("checkbox")[1]).toBeChecked();
  });

  it("saves preferred franchises", async () => {
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const selectedFranchise = { id: "Apex", type: "PRIMARY" };
    const interestedCreatorWithFranchises = {
      ...interestedCreator,
      preferredFranchises: [selectedFranchise],
      creatorProgram: {
        code: "affiliate"
      }
    };
    const service = {
      saveInterestedCreatorInformation: jest.fn().mockResolvedValue({
        data: interestedCreatorWithFranchises
      })
    } as unknown as InterestedCreatorsService;
    (InterestedCreatorsService as jest.Mock).mockReturnValue(service);
    render(
      <InterestedCreatorsFranchisesYouPlayForm
        {...interestedCreatorsFranchisesYouPlayFormProps}
        analytics={analytics}
      />
    );
    await userEvent.click(
      screen.getByRole("textbox", { name: interestedCreatorsFranchisesYouPlayFormLabels.labels.primaryFranchise })
    );
    // Select Primary franchise
    await userEvent.click(screen.getByText("Apex", { selector: ".search-option-label" }));
    // Wait for submit button to be enabled
    const submitButton = screen.getByText(interestedCreatorsFranchisesYouPlayFormLabels.buttons.submit);
    await waitFor(() => expect(submitButton).toBeEnabled());

    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(service.saveInterestedCreatorInformation).toHaveBeenCalledTimes(1);
      expect(service.saveInterestedCreatorInformation).toHaveBeenCalledWith(interestedCreatorWithFranchises);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: true,
        locale: "en-us",
        page: "/"
      });
      expect(router.push).toBeCalledTimes(1);
      expect(router.push).toBeCalledWith("/interested-creators/complete");
    });
  });

  it("shows 'Load more' button if there are more than 8 franchises", async () => {
    const franchises = [
      { value: "a0GK000000S0rHmMAJ", label: "testdisble3", image: "" },
      { value: "a0GK000000RpU56MAF", label: "Need for Speed", image: "" },
      { value: "a0GK000000RpSuCMAV", label: "Apex Legends", image: "" },
      { value: "a0GK000000RpSuRMAV", label: "Madden NFL", image: "" },
      { value: "a0GK000000RpSuMMAA", label: "Test", image: "" },
      { value: "a0GK000000RpSuMPPA", label: "Test 2021", image: "" },
      { value: "a0GK000000RpKuFMMM", label: "Fifa 2020", image: "" },
      { value: "a0GK000000RpKuFMAP", label: "The Sims", image: "" },
      { value: "a0GK000000RpKuFMAV", label: "Fifa 2021", image: "" }
    ];
    render(
      <InterestedCreatorsFranchisesYouPlayForm
        {...interestedCreatorsFranchisesYouPlayFormProps}
        franchises={franchises}
      />
    );
    await userEvent.click(
      screen.getByRole("textbox", { name: interestedCreatorsFranchisesYouPlayFormLabels.labels.primaryFranchise })
    );
    await userEvent.click(screen.getByText("Apex Legends", { selector: ".search-option-label" }));
    const loadMoreButton = screen.queryByText("Load more...");
    await waitFor(() => expect(loadMoreButton).toBeInTheDocument());

    await userEvent.click(loadMoreButton as HTMLElement);

    await waitFor(() => expect(screen.getByText(/Fifa 2021$/i)).toBeVisible());
  });

  it("keeps last selected franchise when input looses focus", async () => {
    render(<InterestedCreatorsFranchisesYouPlayForm {...interestedCreatorsFranchisesYouPlayFormProps} />);
    const submitButton = screen.getByText(interestedCreatorsFranchisesYouPlayFormLabels.buttons.submit);
    expect(submitButton).toBeDisabled();
    const primaryFranchise = screen.getByRole("textbox", {
      name: interestedCreatorsFranchisesYouPlayFormLabels.labels.primaryFranchise
    });
    await userEvent.click(primaryFranchise);
    await userEvent.click(screen.getByText("Apex", { selector: ".search-option-label" }));
    await userEvent.clear(primaryFranchise);
    await waitFor(() => expect(primaryFranchise).toHaveValue(""));

    await userEvent.tab();

    await waitFor(() => {
      expect(primaryFranchise).toHaveValue("Apex");
      expect(submitButton).toBeEnabled();
    });
  });

  it("saves preferred franchises with additional links added", async () => {
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const selectedFranchise = { id: "Fifa", type: "PRIMARY" };
    const interestedCreatorWithFranchises = {
      ...interestedCreator,
      preferredFranchises: [selectedFranchise],
      creatorProgram: {
        code: "affiliate"
      }
    };
    const service = {
      saveInterestedCreatorInformation: jest.fn().mockResolvedValue({
        data: interestedCreatorWithFranchises
      })
    } as unknown as InterestedCreatorsService;
    (InterestedCreatorsService as jest.Mock).mockReturnValue(service);
    render(
      <InterestedCreatorsFranchisesYouPlayForm
        {...interestedCreatorsFranchisesYouPlayFormProps}
        analytics={analytics}
      />
    );
    await userEvent.click(
      screen.getByRole("textbox", { name: interestedCreatorsFranchisesYouPlayFormLabels.labels.primaryFranchise })
    );
    // Select Primary franchise
    await userEvent.click(screen.getByText(/Fifa/i, { selector: ".search-option-label" }));
    // Wait for submit button to be enabled
    const submitButton = screen.getByText(interestedCreatorsFranchisesYouPlayFormLabels.buttons.submit);
    await waitFor(() => expect(submitButton).toBeEnabled());

    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(service.saveInterestedCreatorInformation).toHaveBeenCalledTimes(1);
      expect(service.saveInterestedCreatorInformation).toHaveBeenCalledWith(interestedCreatorWithFranchises);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: true,
        locale: "en-us",
        page: "/"
      });
      expect(router.push).toBeCalledTimes(1);
      expect(router.push).toBeCalledWith("/interested-creators/complete");
    });
  });
});
