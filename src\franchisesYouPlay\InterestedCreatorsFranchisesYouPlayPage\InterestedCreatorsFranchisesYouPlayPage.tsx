import React, { FC, ReactElement, useCallback, useEffect, useState } from "react";
import {
  BrowserAnalytics,
  ERROR,
  LOADING,
  onToastClose,
  toastContent,
  VALIDATION_ERROR,
  ValidationError
} from "../../utils";
import {
  FranchisesYouPlayFallbackImages,
  FranchisesYouPlayFormLabels,
  FranchisesYouPlayLabels
} from "../FranchisesYouPlay";
import { NextRouter } from "next/router";
import { CloseHandler, Dispatch, ErrorHandling } from "../../utils";
import { InterestedCreator } from "../../Browser/InterestedCreatorsService";
import { Toast } from "@eait-playerexp-cn/core-ui-kit";
import InterestedCreatorsFranchisesYouPlayForm from "../InterestedCreatorsFranchisesYouPlayForm/InterestedCreatorsFranchisesYouPlayForm";
import CancelRegistrationModal from "../../CancelRegistrationModal/CancelRegistrationModal";
import { Configuration } from "../../information/Information";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { Franchise } from "@eait-playerexp-cn/metadata-types";

export type FranchiseItem = {
  value: string;
  label: string;
  image: string;
};

type InterestedCreatorsFranchisesYouPlayPageProps = {
  interestedCreator: InterestedCreator;
  franchisesYouPlayLabels: FranchisesYouPlayLabels;
  franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels;
  stableDispatch: Dispatch;
  showConfirmation: boolean;
  setShowConfirmation: (value: boolean) => void;
  onClose: () => void;
  isError?: boolean;
  isValidationError: ValidationError[];
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  unhandledError: string;
  router: NextRouter;
  locale: string;
  analytics: BrowserAnalytics;
  handleCancelRegistration: () => void;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  errorHandling: ErrorHandling;
  configuration: Configuration;
  redirectedToNextStepUrl: string;
  franchisesYouPlayFallbackImages: FranchisesYouPlayFallbackImages;
  basePath?: string;
};

const InterestedCreatorsFranchisesYouPlayPage: FC<InterestedCreatorsFranchisesYouPlayPageProps> = ({
  interestedCreator,
  franchisesYouPlayLabels,
  franchisesYouPlayFormLabels,
  stableDispatch,
  showConfirmation,
  setShowConfirmation,
  onClose,
  isError,
  isValidationError,
  errorToast,
  unhandledError,
  router,
  handleCancelRegistration,
  analytics,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  errorHandling,
  configuration,
  redirectedToNextStepUrl,
  franchisesYouPlayFallbackImages,
  basePath,
}) => {
  const [franchises, setFranchises] = useState<Franchise[]>([]);
  const handleModalClose = useCallback(() => setShowConfirmation(false), [setShowConfirmation]);
  const metadataService = new MetadataService(configuration.metadataClient);
  const modalLabels = {
    title: franchisesYouPlayLabels.modalConfirmationTitle,
    yes: franchisesYouPlayLabels?.buttons?.yes,
    no: franchisesYouPlayLabels?.buttons?.no,
    close: franchisesYouPlayLabels?.buttons?.close,
    confirmationDesc1: franchisesYouPlayLabels.confirmationDesc1,
    confirmationDesc2: franchisesYouPlayLabels.confirmationDesc2
  };

  useEffect(() => {
    stableDispatch({ type: LOADING, data: true });
    async function fetchData() {
      try {
        const allFranchises = await metadataService.getFranchises();
        const supportedFranchises = configuration.supportedFranchises
          ? allFranchises.filter((franchise) => configuration.supportedFranchises?.includes(franchise.value))
          : allFranchises;
        setFranchises(supportedFranchises);
        stableDispatch({ type: LOADING, data: false });
      } catch (e) {
        stableDispatch({ type: LOADING, data: false });
        errorHandling(stableDispatch, e as unknown as Error);
      }
    }
    fetchData();
  }, [stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(<Toast header={unhandledError} content={isError ? isError : toastContent(isValidationError)} />, {
        onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
      });
    }
  }, [isError, isValidationError, stableDispatch, unhandledError, errorToast]);

  return (
    <div className="mg-ic-ui-page-interested-creator">
      <div className="interested-creators-ui-franchises-container">
        <div className="mg-ic-ui-franchises-you-play">
          <h3 className="mg-ic-ui-franchises-you-play-title">{franchisesYouPlayLabels.title}</h3>
          <div className="mg-ic-ui-franchises-you-play-description">{franchisesYouPlayLabels.description}</div>
        </div>
        {franchises && interestedCreator && (
          <InterestedCreatorsFranchisesYouPlayForm
            interestedCreator={interestedCreator}
            franchises={franchises}
            franchisesYouPlayFormLabels={franchisesYouPlayFormLabels}
            stableDispatch={stableDispatch}
            onClose={onClose}
            router={router}
            analytics={analytics}
            INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
            configuration={configuration}
            errorHandling={errorHandling}
            redirectedToNextStepUrl={redirectedToNextStepUrl}
            franchisesYouPlayFallbackImages={franchisesYouPlayFallbackImages}
            basePath={basePath}
          />
        )}
        {showConfirmation && (
          <CancelRegistrationModal
            labels={modalLabels}
            handleCancelRegistration={handleCancelRegistration}
            handleModalClose={handleModalClose}
          />
        )}
      </div>
    </div>
  );
};

export default InterestedCreatorsFranchisesYouPlayPage;
