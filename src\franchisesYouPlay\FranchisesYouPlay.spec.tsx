import React, { act } from "react";
import { render, screen } from "@testing-library/react";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { anInterestedCreator } from "../../test/factories/InterestedCreatorFactories";
import { BrowserAnalytics } from "../utils";
import { franchisesYouPlayLabels } from "../../test/translations/FranchisesYouPlay";
import { NextRouter } from "next/router";
import { useRouter } from "next/router";
import InterestedCreatorFranchisesYouPlay from "./FranchisesYouPlay";
import { InterestedCreator } from "../Browser/InterestedCreatorsService";
import InterestedCreatorsFranchisesYouPlayPage from "./InterestedCreatorsFranchisesYouPlayPage/InterestedCreatorsFranchisesYouPlayPage";
import Random from "../../test/factories/Random";
import { axe } from "jest-axe";
import { Configuration } from "../information/Information";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { aPrimaryFranchise, aSecondaryFranchise } from "../../test/factories/FranchiseFactories";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));
jest.mock("./InterestedCreatorsFranchisesYouPlayPage/InterestedCreatorsFranchisesYouPlayPage", () => {
  return jest.fn(() => <div data-testid="interested-creators-ui-franchises-you-play-page" />);
});

describe("FranchisesYouPlay", () => {
  const interestedCreator = anInterestedCreator({
    nucleusId: Random.nucleusId(),
    defaultGamerTag: Random.uuid(),
    preferredFranchises: [{ id: "Fifa", type: "PRIMARY" }]
  }) as InterestedCreator;
  const analytics = {} as unknown as BrowserAnalytics;
  const router = { locale: "en-us", push: jest.fn() };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  const configuration = { metadataClient: {}, applicationsClient: {}, programCode: "affiliate" } as Configuration;
  const interestedCreatorFranchisesYouPlayProps = {
    interestedCreator,
    handleCancelRegistration: jest.fn(),
    analytics,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    state: {
      onboardingSteps: steps
    },
    redirectedToNextStepUrl: "/interested-creators/confirmation",
    stableDispatch: jest.fn(),
    errorHandling: jest.fn(),
    labels: franchisesYouPlayLabels,
    onClose: jest.fn(),
    showConfirmation: true,
    setShowConfirmation: jest.fn(),
    configuration: configuration,
    locale: "en-us",
    router: router as unknown as NextRouter,
    basePath: "/support-a-creator",
    errorToast: jest.fn(),
    franchisesYouPlayFallbackImages: {
      primaryFranchisefallbackImage: "",
      secondaryFranchisefallbackImage: ""
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    const franchise = [
      aPrimaryFranchise({ value: "Apex", label: "Apex" }),
      aSecondaryFranchise({ value: "Fifa", label: "Fifa" })
    ];
    (MetadataService as jest.Mock).mockReturnValue({
      getFranchises: jest.fn().mockResolvedValue(franchise)
    });
  });

  it("renders the InterestedCreatorsFranchisesYouPlayPage component", () => {
    render(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);

    expect(screen.getByTestId("interested-creators-ui-franchises-you-play-page")).toBeInTheDocument();
  });

  it("passes the correct props to InterestedCreatorsFranchisesYouPlayPage", () => {
    render(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);

    expect(InterestedCreatorsFranchisesYouPlayPage).toHaveBeenCalledWith(
      expect.objectContaining({
        interestedCreator: interestedCreatorFranchisesYouPlayProps.interestedCreator,
        franchisesYouPlayLabels: {
          ...interestedCreatorFranchisesYouPlayProps.labels.franchisesYouPlay,
          buttons: interestedCreatorFranchisesYouPlayProps.labels.layout.buttons
        },
        franchisesYouPlayFormLabels: {
          ...interestedCreatorFranchisesYouPlayProps.labels.franchisesYouPlayFormLabels,
          buttons: interestedCreatorFranchisesYouPlayProps.labels.layout.buttons
        },
        stableDispatch: interestedCreatorFranchisesYouPlayProps.stableDispatch,
        isError: false,
        isValidationError: false,
        errorToast: interestedCreatorFranchisesYouPlayProps.errorToast,
        unhandledError: interestedCreatorFranchisesYouPlayProps.labels.layout.main.unhandledError,
        router: interestedCreatorFranchisesYouPlayProps.router,
        locale: interestedCreatorFranchisesYouPlayProps.locale,
        analytics: interestedCreatorFranchisesYouPlayProps.analytics,
        INTERESTED_CREATOR_REAPPLY_PERIOD: interestedCreatorFranchisesYouPlayProps.INTERESTED_CREATOR_REAPPLY_PERIOD,
        errorHandling: interestedCreatorFranchisesYouPlayProps.errorHandling,
        configuration: interestedCreatorFranchisesYouPlayProps.configuration,
        redirectedToNextStepUrl: interestedCreatorFranchisesYouPlayProps.redirectedToNextStepUrl,
        franchisesYouPlayFallbackImages: interestedCreatorFranchisesYouPlayProps.franchisesYouPlayFallbackImages,
        onClose: expect.any(Function)
      }),
      {}
    );
  });

  it("is accessible", async () => {
    const { container } = render(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);

    const results = await act(() => axe(container));

    expect(results).toHaveNoViolations();
  });
});
