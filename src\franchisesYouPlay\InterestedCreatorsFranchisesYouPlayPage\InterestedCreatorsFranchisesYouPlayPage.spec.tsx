import React, { act } from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { useRouter } from "next/router";
import { axe } from "jest-axe";
import { anInterestedCreator } from "../../../test/factories/InterestedCreatorFactories";
import { franchisesYouPlayLabels } from "../../../test/translations/FranchisesYouPlay";
import { BrowserAnalytics, LOADING } from "../../utils";
import { aPrimaryFranchise, aSecondaryFranchise } from "../../../test/factories/FranchiseFactories";
import { InterestedCreator } from "../../Browser/InterestedCreatorsService";
import InterestedCreatorsFranchisesYouPlayPage from "./InterestedCreatorsFranchisesYouPlayPage";
import Random from "../../../test/factories/Random";
import { Configuration } from "../../information/Information";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));

describe("InterestedCreatorsFranchisesYouPlayPage", () => {
  const locale = "en-us";
  const configuration = { metadataClient: {}, applicationsClient: {}, programCode: "affiliate" } as Configuration;
  (useRouter as jest.Mock).mockImplementation(() => ({ push: jest.fn(), locale }));
  const interestedCreatorsFranchisesYouPlayPageProps = {
    errorHandling: jest.fn(),
    redirectedToNextStepUrl: "/interested-creators/confrimation",
    interestedCreator: anInterestedCreator({
      nucleusId: Random.nucleusId(),
      defaultGamerTag: Random.uuid(),
      preferredFranchises: [{ id: "Fifa", type: "PRIMARY" }]
    }) as InterestedCreator,
    franchisesYouPlayLabels: franchisesYouPlayLabels.franchisesYouPlay,
    franchisesYouPlayFormLabels: franchisesYouPlayLabels.franchisesYouPlayFormLabels,
    stableDispatch: jest.fn(),
    showConfirmation: false,
    setShowConfirmation: jest.fn(),
    onClose: jest.fn(),
    isError: false,
    isValidationError: [],
    errorToast: jest.fn(),
    unhandledError: "",
    router: useRouter(),
    locale,
    analytics: {} as unknown as BrowserAnalytics,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    configuration,
    franchisesYouPlayFallbackImages: {
      primaryFranchisefallbackImage: "",
      secondaryFranchisefallbackImage: ""
    },
    handleCancelRegistration: jest.fn(),
    basePath: "/support-a-creator",
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (MetadataService as jest.Mock).mockReturnValue({
      getFranchises: jest
        .fn()
        .mockResolvedValue([
          aPrimaryFranchise({ value: "Apex", label: "Apex" }),
          aPrimaryFranchise({ value: "Fifa", label: "Fifa" }),
          aSecondaryFranchise({ value: "Battlefield", label: "Battlefield" }),
          aSecondaryFranchise({ value: "Madden", label: "Madden" })
        ])
    });
  });

  it("renders correctly", async () => {
    const { title, description } = interestedCreatorsFranchisesYouPlayPageProps.franchisesYouPlayLabels;
    const { primaryFranchiseTitle, primaryFranchiseSubTitle, secondaryFranchiseTitle, secondaryFranchiseSubTitle } =
      interestedCreatorsFranchisesYouPlayPageProps.franchisesYouPlayFormLabels;

    render(<InterestedCreatorsFranchisesYouPlayPage {...interestedCreatorsFranchisesYouPlayPageProps} />);

    expect(await screen.findByText(title)).toBeInTheDocument();
    expect(screen.getByText(description)).toBeInTheDocument();
    expect(screen.getByText(primaryFranchiseTitle)).toBeInTheDocument();
    expect(screen.getByText(primaryFranchiseSubTitle)).toBeInTheDocument();
    expect(screen.getByText(secondaryFranchiseTitle)).toBeInTheDocument();
    expect(screen.getByText(secondaryFranchiseSubTitle)).toBeInTheDocument();
  });

  it("shows error toast when there is an error", async () => {
    render(
      <InterestedCreatorsFranchisesYouPlayPage {...interestedCreatorsFranchisesYouPlayPageProps} isError={true} />
    );

    await waitFor(() => expect(interestedCreatorsFranchisesYouPlayPageProps.errorToast).toHaveBeenCalled());
  });

  it("is accessible", async () => {
    const { container } = render(
      <InterestedCreatorsFranchisesYouPlayPage {...interestedCreatorsFranchisesYouPlayPageProps} />
    );

    const results = await act(() => axe(container));

    expect(results).toHaveNoViolations();
  });

  describe("franchise filtering", () => {
    const allFranchises = [
      aPrimaryFranchise({ value: "Apex", label: "Apex" }),
      aPrimaryFranchise({ value: "Fifa", label: "Fifa" }),
      aSecondaryFranchise({ value: "Battlefield", label: "Battlefield" }),
      aSecondaryFranchise({ value: "Madden", label: "Madden" })
    ];

    beforeEach(() => jest.clearAllMocks());

    it("it shows all franchises if no filter is specified", async () => {
      const metadataService = { getFranchises: jest.fn().mockResolvedValue(allFranchises) };
      (MetadataService as jest.Mock).mockReturnValue(metadataService);
      const configWithoutSupportedFranchises = { ...configuration };

      render(
        <InterestedCreatorsFranchisesYouPlayPage
          {...interestedCreatorsFranchisesYouPlayPageProps}
          configuration={configWithoutSupportedFranchises}
        />
      );

      await waitFor(() => {
        expect(metadataService.getFranchises).toHaveBeenCalledTimes(1);
        expect(metadataService.getFranchises).toHaveBeenCalledWith();
        expect(interestedCreatorsFranchisesYouPlayPageProps.stableDispatch).toHaveBeenCalledWith({
          type: LOADING,
          data: false
        });
      });
      // Verify all franchises are available
      const franchiseNames = screen.getAllByRole("checkbox").map((checkbox) => checkbox.getAttribute("aria-label"));
      expect(franchiseNames).toContain("Apex");
      expect(franchiseNames).toContain("Fifa");
      expect(franchiseNames).toContain("Battlefield");
      expect(franchiseNames).toContain("Madden");
    });

    it("it shows only supported franchises", async () => {
      const configWithSupportedFranchises = {
        ...configuration,
        supportedFranchises: ["Apex", "Fifa"]
      };
      const metadataService = { getFranchises: jest.fn().mockResolvedValue(allFranchises) };
      (MetadataService as jest.Mock).mockReturnValue(metadataService);

      render(
        <InterestedCreatorsFranchisesYouPlayPage
          {...interestedCreatorsFranchisesYouPlayPageProps}
          configuration={configWithSupportedFranchises}
        />
      );

      await waitFor(() => {
        expect(metadataService.getFranchises).toHaveBeenCalled();
        expect(interestedCreatorsFranchisesYouPlayPageProps.stableDispatch).toHaveBeenCalledWith({
          type: LOADING,
          data: false
        });
      });
      // Verify only supported franchises are shown
      const franchiseNames = screen.getAllByRole("checkbox").map((checkbox) => checkbox.getAttribute("aria-label"));
      expect(franchiseNames).toHaveLength(2);
      expect(franchiseNames).toContain("Apex");
      expect(franchiseNames).toContain("Fifa");
      expect(franchiseNames).not.toContain("Battlefield");
      expect(franchiseNames).not.toContain("Madden");
    });

    it("it handles errors when fetching franchises fails", async () => {
      const error = new Error("Failed to fetch franchises");
      const metadataService = { getFranchises: jest.fn().mockRejectedValue(error) };
      (MetadataService as jest.Mock).mockReturnValue(metadataService);

      render(<InterestedCreatorsFranchisesYouPlayPage {...interestedCreatorsFranchisesYouPlayPageProps} />);

      await waitFor(() => {
        // Verify loading is set to false
        expect(interestedCreatorsFranchisesYouPlayPageProps.stableDispatch).toHaveBeenCalledWith({
          type: LOADING,
          data: false
        });
        // Verify error handling is called
        expect(interestedCreatorsFranchisesYouPlayPageProps.errorHandling).toHaveBeenCalledWith(
          interestedCreatorsFranchisesYouPlayPageProps.stableDispatch,
          error
        );
      });
    });
  });
});
